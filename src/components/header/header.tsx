import React, { useEffect } from "react";
import IconButton from "../common/IconButton";
import Icon from "../common/Icon";
import { AuthContext } from "../../context/AuthContext";
import { getApiUrl } from "../../utils/api";
import axios from "axios";

interface HeaderProps {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar }) => {
  const { userInfo, setUserInfo, loading, setLoading } =
    React.useContext(AuthContext);
  console.log(userInfo, "userInfo");

  const getUserInfo = async () => {
    try {
      setLoading(true);
      const res = await axios.get(getApiUrl("/api/auth/user-info"), {
        withCredentials: true,
      });
      setUserInfo(res.data);
      console.log(res, "res");
    } catch (error) {
      console.log(error, "error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getUserInfo();
  }, []);

  return (
    <header className="bg-white border-b border-b-border md:py-4 md:px-10 py-2 px-3">
      <div className="flex items-center">
        <div className="flex items-center">
          <button
            className="cursor-pointer"
            onClick={toggleSidebar}
            type="button"
          >
            <Icon name="MenuIcon" />
          </button>
        </div>
        <div className="flex grow justify-end">
          <div className="flex items-center gap-3 mr-6">
            <IconButton
              icon={
                <Icon name="BellIcon" color="#fff" height={16} width={16} />
              }
              variant="primary"
            />
            <IconButton
              icon={<Icon name="Mail" color="#EA6A12" height={16} width={16} />}
              variant="light"
            />
            <IconButton
              icon={
                <Icon name="Settings" color="#EA6A12" height={16} width={16} />
              }
              variant="light"
            />
          </div>
          <div className="flex items-center gap-4 before:content-[''] relative before:absolute before:bg-[#00000033] before:h-[25px] before:w-[1px]">
            {loading ? (
              <span>Loading..</span>
            ) : (
              <div className="md:flex flex-col md:pl-6 hidden">
                <span className="text-base font-semibold text-[#07143B]">
                  Austin Robertson
                </span>
                <span className="text-sm font-normal text-[#07143BB2]">
                  Marketing Administrator
                </span>
              </div>
            )}
            <div className="max-w-9 cursor-pointer md:max-w-11 ml-6 md:ml-0 border border-border rounded-full overflow-hidden">
              <img src="/profile.png" alt="Profile" className="object-cover" />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
