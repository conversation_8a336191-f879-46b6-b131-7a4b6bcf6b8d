import React, { useState } from "react";
import { useForm } from "react-hook-form";
import type { SubmitHandler } from "react-hook-form";
import axios from "axios";
import toast from "react-hot-toast";
import InputField from "../common/InputField";
import CustomButton from "../common/CustomButton";
import { getApiUrl } from "../../utils/api";
import { useAuth } from "../../context/AuthContext";

// Interface for category form data
interface CategoryFormData {
  name: string;
  description: string;
}

const CategoriesForm: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { authToken } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CategoryFormData>();

  // Handle form submission
  const handleAddCategory: SubmitHandler<CategoryFormData> = async (data) => {
    try {
      setLoading(true);

      // Prepare the data for API
      const categoryData = {
        name: data.name,
        description: data.description,
        outlet: "2b9971d41f2b4b69_1749317404438", // Fixed outlet value
      };

      const res = await axios.post(
        getApiUrl("/restaurant/categories/"),
        categoryData,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        },
      );

      toast.success("Category added successfully!");
      reset(); // Reset form after successful submission
    } catch (error) {
      console.error("Error adding category:", error);
      toast.error("Failed to add category");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        Add New Category
      </h2>

      <form onSubmit={handleSubmit(handleAddCategory)} className="space-y-6">
        {/* Name Field */}
        <div>
          <InputField
            id="name"
            type="text"
            label="Category Name *"
            placeholder="Enter category name"
            {...register("name", {
              required: "Category name is required",
              minLength: {
                value: 2,
                message: "Name must be at least 2 characters long",
              },
            })}
            error={errors.name?.message}
          />
        </div>

        {/* Description Field */}
        <div>
          <label
            htmlFor="description"
            className="block text-sm font-medium mb-1"
          >
            Description *
          </label>
          <textarea
            id="description"
            {...register("description", {
              required: "Description is required",
              minLength: {
                value: 10,
                message: "Description must be at least 10 characters long",
              },
            })}
            placeholder="Enter category description"
            rows={4}
            className={`w-full rounded-md border border-[#E6E6E6] px-3 py-2 text-sm outline-none focus:border-[var(--color-orange)] transition-all duration-200 resize-vertical ${
              errors.description ? "border-red-500" : ""
            }`}
          />
          {errors.description && (
            <p className="mt-1 text-xs text-red-500">
              {errors.description.message}
            </p>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <CustomButton
            label="Cancel"
            onClick={() => reset()}
            bgColor="bg-gray-500"
            textColor="text-white"
            className="px-6 py-2"
          />
          <CustomButton
            label={loading ? "Adding..." : "Add Category"}
            bgColor="bg-[var(--color-orange)]"
            textColor="text-white"
            className="px-6 py-2"
            disabled={loading}
          />
        </div>
      </form>
    </div>
  );
};

export default CategoriesForm;
