import React, { createContext, useState, type ReactNode } from "react";

export const AuthContext = createContext();

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [userInfo, setUserInfo] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  console.log("AuthProvider initialized");

  return (
    <AuthContext.Provider value={{ userInfo, setUserInfo , loading, setLoading }}>
      {children}
    </AuthContext.Provider>
  );
};
