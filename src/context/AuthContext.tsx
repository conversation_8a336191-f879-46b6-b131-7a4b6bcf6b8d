import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from "react";

type AuthContextType = {
  authToken: string | null;
  setAuthToken: (token: string | null) => void;
};

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined,
);

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [authToken, setAuthTokenState] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);


  // Custom setAuthToken that also updates localStorage
  const setAuthToken = (token: string | null) => {
    setAuthTokenState(token);
    if (token) {
      localStorage.setItem("temp_token", token);
    } else {
      localStorage.removeItem("temp_token");
    }
  };

  const checkAuth = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("temp_token");
      setAuthTokenState(token);
      // const res = await axios.post(getApiUrl(`/profile/auth/token/refresh/`), {
      //   authToken,
      // });
    } catch (e) {
      console.log(e, "error from context");
    } finally {
      setLoading(false);
    }
  };


  if (loading) return <h1>loading...</h1>;

  useEffect(() => {
    checkAuth();
  }, []);

  return (
    <AuthContext.Provider value={{ authToken, setAuthToken }}>
      {children}
    </AuthContext.Provider>
  );
};
