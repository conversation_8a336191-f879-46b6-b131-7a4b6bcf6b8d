import React, { useState } from "react";
import { useForm } from "react-hook-form";
import type { Submit<PERSON>and<PERSON> } from "react-hook-form";
import axios from "axios";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import InputField from "../../components/common/InputField";
import CustomButton from "../../components/common/CustomButton";
import Icon from "../../components/common/Icon";
import { getApiUrl } from "../../utils/api";
import { useAuth } from "../../context/AuthContext";

// Interface for login form data
interface LoginFormData {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { setAuthToken } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>();

  // Handle form submission
  const handleLogin: SubmitHandler<LoginFormData> = async (data) => {
    try {
      setLoading(true);

      const res = await axios.post(getApiUrl("/api/auth/login"), data, {
        withCredentials: true,
      });

      if (res?.data?.access) {
        setAuthToken(res.data.access);
        toast.success("Login successful!");

        // Redirect to menu list page after successful login
        navigate("/");
      } else {
        toast.error("Invalid response from server");
      }
    } catch (error: any) {
      console.error("Login error:", error);

      // Handle different error scenarios
      if (error.response?.status === 401) {
        toast.error("Invalid email or password");
      } else if (error.response?.status >= 500) {
        toast.error("Server error. Please try again later.");
      } else {
        toast.error("Login failed. Please check your credentials.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center ">
      <div className="max-w-[400px] w-full">
        <div className="max-w-xl w-full">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-semibold text-gray-700 mb-2">
              Welcome Back
            </h2>
            <p className="text-gray-600">Please sign in to your account</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <form onSubmit={handleSubmit(handleLogin)} className="space-y-6">
              {/* Email Field */}
              <div>
                <InputField
                  id="email"
                  type="email"
                  label="Email Address *"
                  placeholder="Enter your email"
                  icon={<Icon name="Mail" width={20} height={20} />}
                  iconPosition="left"
                  {...register("email", {
                    required: "Email is required",
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: "Invalid email address",
                    },
                  })}
                  error={errors.email?.message}
                />
              </div>

              {/* Password Field */}
              <div>
                <InputField
                  id="password"
                  type="password"
                  label="Password *"
                  placeholder="Enter your password"
                  icon={<Icon name="Lock" width={20} height={20} />}
                  iconPosition="left"
                  {...register("password", {
                    required: "Password is required",
                    minLength: {
                      value: 6,
                      message: "Password must be at least 6 characters long",
                    },
                  })}
                  error={errors.password?.message}
                />
              </div>

              {/* Submit Button */}
              <div>
                <CustomButton
                  label={loading ? "Signing in..." : "Sign In"}
                  bgColor="bg-[var(--color-orange)]"
                  textColor="text-white"
                  className="w-full py-3 text-base font-medium"
                  disabled={loading}
                />
              </div>
            </form>

            {/* Additional Links */}
            {/* <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Don't have an account?{" "}
                <a href="#" className="text-orange font-medium hover:underline">
                  Contact Administrator
                </a>
              </p>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
